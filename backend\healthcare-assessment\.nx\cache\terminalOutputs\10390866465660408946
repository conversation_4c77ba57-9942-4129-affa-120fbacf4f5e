chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 63.5 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
webpack compiled [1m[32msuccessfully[39m[22m (c826bd7e36ed6eda)
[1m[31mStarting inspector on localhost:9229 failed: addr[39m[22m
[1m[31mess already in use[39m[22m
[1m[31m[39m[22m
[1m[31m[31m[Nest] 37556  - [39m[31m31/07/2025, 5:43:35 pm [31m  ERROR[39m[31m [38;5;3m[MongooseModule] [39m[31m[31mUnable to connect to the database. Retrying (1)...[39m[31m[39m[22m
[1m[31m[39m[22m
[1m[31mMongooseServerSelectionError: connect ECONNREFUSED ::1:27017[39m[22m
[1m[31m    at _handleConnectionErrors (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:816:11)[39m[22m
[1m[31m    at NativeConnection.asPromise (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:1278:11)[39m[22m
[1m[31m    at C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\@nestjs\mongoose\dist\mongoose-core.module.js:65:112[39m[22m
[1m[31m[39m[22m
[1m[31m[31m[Nest] 37556  - [39m[31m31/07/2025, 5:44:08 pm [31m  ERROR[39m[31m [38;5;3m[MongooseModule] [39m[31m[31mUnable to connect to the database. Retrying (2)...[39m[31m[39m[22m
[1m[31mMongooseServerSelectionError: connect ECONNREFUSED ::1:27017[39m[22m
[1m[31m    at _handleConnectionErrors (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:816:11)[39m[22m
[1m[31m    at NativeConnection.asPromise (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:1278:11)[39m[22m
[1m[31m    at C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\@nestjs\mongoose\dist\mongoose-core.module.js:65:112[39m[22m
[1m[31m[39m[22m
[1m[31m[31m[Nest] 37556  - [39m[31m31/07/2025, 5:44:41 pm [31m  ERROR[39m[31m [38;5;3m[MongooseModule] [39m[31m[31mUnable to connect to the database. Retrying (3)...[39m[31m[39m[22m
[1m[31m[39m[22m
[1m[31mMongooseServerSelectionError: connect ECONNREFUSED ::1:27017[39m[22m
[1m[31m    at _handleConnectionErrors (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:816:11)[39m[22m
[1m[31m    at NativeConnection.asPromise (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:1278:11)[39m[22m
[1m[31m    at C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\@nestjs\mongoose\dist\mongoose-core.module.js:65:112[39m[22m
[1m[31m[39m[22m
[1m[31m[31m[Nest] 37556  - [39m[31m31/07/2025, 5:45:14 pm [31m  ERROR[39m[31m [38;5;3m[MongooseModule] [39m[31m[31mUnable to connect to the database. Retrying (4)...[39m[31m[39m[22m
[1m[31mMongooseServerSelectionError: connect ECONNREFUSED ::1:27017[39m[22m
[1m[31m    at _handleConnectionErrors (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:816:11)[39m[22m
[1m[31m    at NativeConnection.asPromise (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:1278:11)[39m[22m
[1m[31m    at C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\@nestjs\mongoose\dist\mongoose-core.module.js:65:112[39m[22m
[1m[31m[39m[22m
[1m[31m[31m[Nest] 37556  - [39m[31m31/07/2025, 5:45:47 pm [31m  ERROR[39m[31m [38;5;3m[MongooseModule] [39m[31m[31mUnable to connect to the database. Retrying (5)...[39m[31m[39m[22m
[1m[31m[39m[22m
[1m[31mMongooseServerSelectionError: connect ECONNREFUSED ::1:27017[39m[22m
[1m[31m    at _handleConnectionErrors (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:816:11)[39m[22m
[1m[31m    at NativeConnection.asPromise (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:1278:11)[39m[22m
[1m[31m    at C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\@nestjs\mongoose\dist\mongoose-core.module.js:65:112[39m[22m
[1m[31m[39m[22m
[1m[31m[31m[Nest] 37556  - [39m[31m31/07/2025, 5:46:20 pm [31m  ERROR[39m[31m [38;5;3m[MongooseModule] [39m[31m[31mUnable to connect to the database. Retrying (6)...[39m[31m[39m[22m
[1m[31m[39m[22m
[1m[31mMongooseServerSelectionError: connect ECONNREFUSED ::1:27017[39m[22m
[1m[31m    at _handleConnectionErrors (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:816:11)[39m[22m
[1m[31m    at NativeConnection.asPromise (C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\mongoose\lib\connection.js:1278:11)[39m[22m
[1m[31m    at C:\Users\<USER>\Documents\assessmentproject\backend\healthcare-assessment\node_modules\@nestjs\mongoose\dist\mongoose-core.module.js:65:112[39m[22m
[1m[31m[39m[22m
