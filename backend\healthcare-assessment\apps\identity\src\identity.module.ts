import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { join } from 'path';

// Entities and Schemas
import { User, UserSchema } from './entities/user.entity';
import { FacilityType, FacilityTypeSchema } from './entities/facility-type.entity';
import { ServiceLine, ServiceLineSchema } from './entities/service-line.entity';

// Services
import { AuthService } from './services/auth.service';
import { SeedService } from './services/seed.service';

// Resolvers
import { AuthResolver } from './resolvers/auth.resolver';
import { UserResolver } from './resolvers/user.resolver';

// Controllers
import { AuthController } from './controllers/auth.controller';

// Guards and Strategies
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { JwtStrategy } from './strategies/jwt.strategy';

import { DatabaseModule } from '@app/db';  // Import the DatabaseModule here
import { EmailModule } from '@app/email';  // Import EmailModule


@Module({
  imports: [
    DatabaseModule,
    EmailModule,
    PassportModule,
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
      sortSchema: true,
      playground: true,
      introspection: true,
      context: ({ req }) => ({ req }),
    }),
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: FacilityType.name, schema: FacilityTypeSchema },
      { name: ServiceLine.name, schema: ServiceLineSchema },
    ]),
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'healthcare-assessment-secret',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  providers: [
    AuthService,
    SeedService,
    AuthResolver,
    UserResolver,
    JwtStrategy,
    JwtAuthGuard,
  ],
  controllers: [AuthController],
  exports: [AuthService, SeedService],
})
export class IdentityAppModule { }
