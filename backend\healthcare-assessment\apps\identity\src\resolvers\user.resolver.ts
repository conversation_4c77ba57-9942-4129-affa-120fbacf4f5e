import { Resolver, Query, Args } from '@nestjs/graphql';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { FacilityType } from '../entities/facility-type.entity';
import { ServiceLine } from '../entities/service-line.entity';

@Resolver()
export class UserResolver {
  constructor(
    @InjectModel(FacilityType.name) private facilityTypeModel: Model<FacilityType>,
    @InjectModel(ServiceLine.name) private serviceLineModel: Model<ServiceLine>,
  ) {}

  @Query(() => [FacilityType])
  async getFacilityTypes(): Promise<FacilityType[]> {
    return this.facilityTypeModel.find({ isActive: true });
  }

  @Query(() => [ServiceLine])
  async getServiceLines(): Promise<ServiceLine[]> {
    return this.serviceLineModel.find({ isActive: true });
  }
}