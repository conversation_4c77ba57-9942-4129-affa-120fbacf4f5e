{"nodes": {"notification": {"name": "notification", "type": "lib", "data": {"root": "libs/notification", "name": "notification", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/notification/src", "projectType": "library", "tags": ["scope:shared", "type:feature"], "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/notification", "main": "libs/notification/src/index.ts", "tsConfig": "libs/notification/tsconfig.lib.json", "assets": ["libs/notification/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/notification/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/notification/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "implicitDependencies": []}}, "permissions": {"name": "permissions", "type": "lib", "data": {"root": "libs/permissions", "name": "permissions", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/permissions/src", "projectType": "library", "tags": ["scope:shared", "type:feature"], "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/permissions", "main": "libs/permissions/src/index.ts", "tsConfig": "libs/permissions/tsconfig.lib.json", "assets": ["libs/permissions/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/permissions/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/permissions/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "implicitDependencies": []}}, "identity": {"name": "identity", "type": "app", "data": {"root": "apps/identity", "name": "identity", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/identity/src", "projectType": "application", "tags": ["scope:identity", "type:app"], "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/identity", "main": "apps/identity/src/main.ts", "tsConfig": "apps/identity/tsconfig.app.json", "webpackConfig": "apps/identity/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "identity:build"}, "configurations": {"development": {"buildTarget": "identity:build:development"}, "production": {"buildTarget": "identity:build:production"}}}}, "implicitDependencies": []}}, "common": {"name": "common", "type": "lib", "data": {"root": "libs/common", "name": "common", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/common/src", "projectType": "library", "tags": ["scope:shared", "type:util"], "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/common", "main": "libs/common/src/index.ts", "tsConfig": "libs/common/tsconfig.lib.json", "assets": ["libs/common/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/common/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/common/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "implicitDependencies": []}}, "config": {"name": "config", "type": "lib", "data": {"root": "libs/config", "name": "config", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/config/src", "projectType": "library", "tags": ["scope:shared", "type:util"], "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/config", "main": "libs/config/src/index.ts", "tsConfig": "libs/config/tsconfig.lib.json", "assets": ["libs/config/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/config/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/config/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "implicitDependencies": []}}, "audit": {"name": "audit", "type": "lib", "data": {"root": "libs/audit", "name": "audit", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/audit/src", "projectType": "library", "tags": ["scope:shared", "type:feature"], "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/audit", "main": "libs/audit/src/index.ts", "tsConfig": "libs/audit/tsconfig.lib.json", "assets": ["libs/audit/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/audit/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/audit/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "implicitDependencies": []}}, "email": {"name": "email", "type": "lib", "data": {"root": "libs/email", "name": "email", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/email/src", "projectType": "library", "tags": ["scope:shared", "type:feature"], "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/email", "main": "libs/email/src/index.ts", "tsConfig": "libs/email/tsconfig.lib.json", "assets": ["libs/email/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/email/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/email/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "implicitDependencies": []}}, "error": {"name": "error", "type": "lib", "data": {"root": "libs/error", "name": "error", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/error/src", "projectType": "library", "tags": ["scope:shared", "type:util"], "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/error", "main": "libs/error/src/index.ts", "tsConfig": "libs/error/tsconfig.lib.json", "assets": ["libs/error/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/error/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/error/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "implicitDependencies": []}}, "db": {"name": "db", "type": "lib", "data": {"root": "libs/db", "name": "db", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/db/src", "projectType": "library", "tags": ["scope:shared", "type:data-access"], "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/db", "main": "libs/db/src/index.ts", "tsConfig": "libs/db/tsconfig.lib.json", "assets": ["libs/db/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/db/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/db/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "implicitDependencies": []}}}, "externalNodes": {}, "dependencies": {"notification": [], "permissions": [], "identity": [{"source": "identity", "target": "db", "type": "static"}, {"source": "identity", "target": "email", "type": "static"}], "common": [], "config": [], "audit": [], "email": [], "error": [], "db": []}, "version": "6.0"}