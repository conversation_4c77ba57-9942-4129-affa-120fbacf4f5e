import { HttpException, HttpStatus } from '@nestjs/common';

export class AuthException extends HttpException {
  constructor(message: string, statusCode: HttpStatus = HttpStatus.UNAUTHORIZED) {
    super(message, statusCode);
  }
}

export class InvalidCredentialsException extends AuthException {
  constructor() {
    super('Invalid credentials provided', HttpStatus.UNAUTHORIZED);
  }
}

export class AccountNotActivatedException extends AuthException {
  constructor() {
    super('Account not activated. Please complete registration.', HttpStatus.FORBIDDEN);
  }
}

export class InvalidOtpException extends AuthException {
  constructor() {
    super('Invalid or expired OTP code', HttpStatus.BAD_REQUEST);
  }
}