import { ReturnTypeFunc } from '../interfaces/return-type-func.interface';
import { ResolveFieldOptions } from './resolve-field.decorator';
/**
 * Property resolver (method) Decorator.
 */
export declare function ResolveProperty(typeFunc?: ReturnTypeFunc, options?: ResolveFieldOptions): MethodDecorator;
/**
 * Property resolver (method) Decorator.
 */
export declare function ResolveProperty(propertyName?: string, typeFunc?: ReturnTypeFunc, options?: ResolveFieldOptions): MethodDecorator;
//# sourceMappingURL=resolve-property.decorator.d.ts.map