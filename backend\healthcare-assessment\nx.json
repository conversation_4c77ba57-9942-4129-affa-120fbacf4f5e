{"$schema": "./node_modules/nx/schemas/nx-schema.json", "npmScope": "healthcare-assessment", "affected": {"defaultBase": "main"}, "cli": {"packageManager": "npm"}, "generators": {"@nx/nest": {"application": {"linter": "eslint"}}, "@nx/js": {"library": {"linter": "eslint"}}}, "defaultProject": "identity", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json"], "sharedGlobals": []}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"]}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"]}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"]}}}