# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type AuthR<PERSON>ponse {
  message: String!
  success: Boolean!
  token: String
  user: User
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type FacilityType {
  createdAt: DateTime!
  description: String
  isActive: Boolean!
  name: String!
  updatedAt: DateTime!
}

input ForgotPasswordInput {
  email: String!
}

input HospitalDetailsInput {
  county: String!
  email: String!
  facilityName: String!
  facilityType: String!
  numberOfLicensedBeds: Float!
  serviceLines: [String!]!
  state: String!
}

type Mutation {
  forgotPassword(input: ForgotPasswordInput!): AuthResponse!
  resetPassword(input: ResetPasswordInput!): AuthResponse!
  setHospitalDetails(input: HospitalDetailsInput!): SignUpResponse!
  setPassword(input: SetPasswordInput!): SignUpResponse!
  signIn(input: SignInInput!): AuthResponse!
  signUp(input: SignUpInput!): SignUpResponse!
  updateProfile(input: UpdateProfileInput!): AuthResponse!
  verifyOtp(input: VerifyOtpInput!): SignUpResponse!
}

type Query {
  getFacilityTypes: [FacilityType!]!
  getProfile: AuthResponse!
  getServiceLines: [ServiceLine!]!
}

input ResetPasswordInput {
  confirmPassword: String!
  password: String!
  token: String!
}

type ServiceLine {
  createdAt: DateTime!
  description: String
  isActive: Boolean!
  name: String!
  updatedAt: DateTime!
}

input SetPasswordInput {
  confirmPassword: String!
  email: String!
  password: String!
}

input SignInInput {
  emailOrPhone: String!
  password: String!
}

input SignUpInput {
  county: String!
  email: String!
  facilityName: String!
  facilityType: String!
  fullName: String!
  phoneNumber: String!
  serviceLines: [String!]
  state: String!
}

type SignUpResponse {
  message: String!
  nextStep: String!
  success: Boolean!
}

input UpdateProfileInput {
  county: String
  facilityName: String
  fullName: String
  numberOfLicensedBeds: Float
  phoneNumber: String
  serviceLines: [String!]
  state: String
}

type User {
  county: String!
  createdAt: DateTime!
  email: String!
  facilityName: String!
  facilityType: String!
  fullName: String!
  isActive: Boolean!
  numberOfLicensedBeds: Float
  phoneNumber: String!
  role: UserRole!
  serviceLines: [String!]
  state: String!
  status: UserStatus!
  updatedAt: DateTime!
}

enum UserRole {
  ADMIN
  SUPER_ADMIN
  USER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING_DETAILS
  PENDING_PASSWORD
  PENDING_VERIFICATION
}

input VerifyOtpInput {
  email: String!
  otpCode: String!
}