import { InputType, Field, ObjectType } from '@nestjs/graphql';
import { IsEmail, IsString, IsPhoneNumber, IsOptional, IsNumber, IsArray, MinLength } from 'class-validator';
import { User } from '../entities/user.entity';

@InputType()
export class SignUpInput {
  @Field()
  @IsString()
  fullName: string;

  @Field()
  @IsEmail()
  email: string;

  @Field()
  @IsString()
  phoneNumber: string;

  @Field()
  @IsString()
  facilityName: string;

  @Field()
  @IsString()
  facilityType: string;

  @Field()
  @IsString()
  state: string;

  @Field()
  @IsString()
  county: string;

  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  serviceLines?: string[];
}

@InputType()
export class VerifyOtpInput {
  @Field()
  @IsEmail()
  email: string;

  @Field()
  @IsString()
  otpCode: string;
}

@InputType()
export class SetPasswordInput {
  @Field()
  @IsEmail()
  email: string;

  @Field()
  @IsString()
  @MinLength(8)
  password: string;

  @Field()
  @IsString()
  confirmPassword: string;
}

@InputType()
export class HospitalDetailsInput {
  @Field()
  @IsEmail()
  email: string;

  @Field()
  @IsNumber()
  numberOfLicensedBeds: number;

  @Field(() => [String])
  @IsArray()
  serviceLines: string[];

  @Field()
  @IsString()
  facilityName: string;

  @Field()
  @IsString()
  facilityType: string;

  @Field()
  @IsString()
  state: string;

  @Field()
  @IsString()
  county: string;
}

@InputType()
export class SignInInput {
  @Field()
  @IsString()
  emailOrPhone: string;

  @Field()
  @IsString()
  password: string;
}

@InputType()
export class ForgotPasswordInput {
  @Field()
  @IsEmail()
  email: string;
}

@InputType()
export class ResetPasswordInput {
  @Field()
  @IsString()
  token: string;

  @Field()
  @IsString()
  @MinLength(8)
  password: string;

  @Field()
  @IsString()
  confirmPassword: string;
}

@InputType()
export class UpdateProfileInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  fullName?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  facilityName?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  state?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  county?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsNumber()
  numberOfLicensedBeds?: number;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  serviceLines?: string[];
}

@ObjectType()
export class AuthResponse {
  @Field()
  success: boolean;

  @Field()
  message: string;

  @Field({ nullable: true })
  token?: string;

  @Field(() => User, { nullable: true })
  user?: User;
}

@ObjectType()
export class SignUpResponse {
  @Field()
  success: boolean;

  @Field()
  message: string;

  @Field()
  nextStep: string; // 'verify_otp', 'set_password', 'hospital_details', 'complete'
}