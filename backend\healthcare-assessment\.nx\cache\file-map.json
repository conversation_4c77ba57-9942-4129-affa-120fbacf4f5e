{"version": "6.0", "nxVersion": "17.2.8", "deps": {"@nestjs/apollo": "^12.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/graphql": "^12.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@types/passport-jwt": "^4.0.1", "apollo-server-express": "^3.12.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "graphql": "^16.8.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.0", "nodemailer": "^6.9.0", "otplib": "^12.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "ulid": "^2.3.0", "@nx/eslint": "17.2.8", "@nx/jest": "17.2.8", "@nx/js": "17.2.8", "@nx/nest": "17.2.8", "@nx/node": "17.2.8", "@nx/webpack": "17.2.8", "@nx/workspace": "17.2.8", "@types/bcryptjs": "^2.4.2", "@types/jest": "^29.4.0", "@types/jsonwebtoken": "^9.0.0", "@types/node": "~18.16.9", "@types/nodemailer": "^6.4.0", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "~8.46.0", "jest": "^29.4.1", "nx": "17.2.8", "ts-jest": "^29.1.0", "typescript": "~5.2.2"}, "pathMappings": {"@app/audit": ["libs/audit/src/index.ts"], "@app/common": ["libs/common/src/index.ts"], "@app/config": ["libs/config/src/index.ts"], "@app/db": ["libs/db/src/index.ts"], "@app/email": ["libs/email/src/index.ts"], "@app/error": ["libs/error/src/index.ts"], "@app/notification": ["libs/notification/src/index.ts"], "@app/permissions": ["libs/permissions/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": ".eslintrc.js", "hash": "10428354086272077971"}, {"file": ".eslintrc.json", "hash": "13046027563728357771"}, {"file": ".giti<PERSON>re", "hash": "625840824947079445"}, {"file": ".prettier<PERSON>", "hash": "761291344349461119"}, {"file": "jest.preset.js", "hash": "11012272483676782930"}, {"file": "nest-cli.json", "hash": "13558312002523647636"}, {"file": "nx.json", "hash": "13605267509500819013"}, {"file": "package.json", "hash": "5759354659117358597"}, {"file": "src/schema.gql", "hash": "10924184098837207443"}, {"file": "tsconfig.base.json", "hash": "1746782359284816086"}, {"file": "tsconfig.json", "hash": "1503746833586163307"}], "projectFileMap": {"email": [{"file": "libs/email/jest.config.ts", "hash": "11331945510011007543"}, {"file": "libs/email/project.json", "hash": "2686650156287857892"}, {"file": "libs/email/src/index.ts", "hash": "6090390694925928827"}, {"file": "libs/email/src/lib/email.module.ts", "hash": "4330811935151941908", "deps": ["npm:@nestjs/common"]}, {"file": "libs/email/src/lib/interfaces/email.interface.ts", "hash": "14466364199104303711"}, {"file": "libs/email/src/lib/services/email.service.ts", "hash": "3059041618659176367", "deps": ["npm:@nestjs/common", "npm:nodemailer"]}, {"file": "libs/email/tsconfig.json", "hash": "13141995473148530199"}, {"file": "libs/email/tsconfig.lib.json", "hash": "5540311948197457956"}, {"file": "libs/email/tsconfig.spec.json", "hash": "3031220043730143498"}], "audit": [{"file": "libs/audit/project.json", "hash": "1627147790194179993"}, {"file": "libs/audit/src/index.ts", "hash": "14628819512207790132"}, {"file": "libs/audit/src/lib/audit.module.ts", "hash": "7305937303859650785", "deps": ["npm:@nestjs/common"]}, {"file": "libs/audit/tsconfig.lib.json", "hash": "1644880041329845813"}], "common": [{"file": "libs/common/jest.config.ts", "hash": "16888734815773975888"}, {"file": "libs/common/project.json", "hash": "13853877162642439590"}, {"file": "libs/common/src/index.ts", "hash": "15754646939700735357"}, {"file": "libs/common/src/lib/common.module.ts", "hash": "18048888836507400458", "deps": ["npm:@nestjs/common"]}, {"file": "libs/common/tsconfig.json", "hash": "13141995473148530199"}, {"file": "libs/common/tsconfig.lib.json", "hash": "15051920992769463824"}, {"file": "libs/common/tsconfig.spec.json", "hash": "3031220043730143498"}], "notification": [{"file": "libs/notification/project.json", "hash": "7102624499312580747"}, {"file": "libs/notification/src/index.ts", "hash": "14311102724212782443"}, {"file": "libs/notification/src/lib/notification.module.ts", "hash": "5055023301593496316", "deps": ["npm:@nestjs/common"]}, {"file": "libs/notification/tsconfig.lib.json", "hash": "1745992962837939316"}], "identity": [{"file": "apps/identity/.env.example", "hash": "13193109523358956259"}, {"file": "apps/identity/Dockerfile", "hash": "5525709865951750926"}, {"file": "apps/identity/jest.config.ts", "hash": "9524162657720316120"}, {"file": "apps/identity/project.json", "hash": "10769516713087701263"}, {"file": "apps/identity/src/controllers/auth.controller.ts", "hash": "10700974424856091763", "deps": ["npm:@nestjs/common"]}, {"file": "apps/identity/src/dto/auth.dto.ts", "hash": "4581822361167414029", "deps": ["npm:@nestjs/graphql", "npm:class-validator"]}, {"file": "apps/identity/src/entities/facility-type.entity.ts", "hash": "16355159179543415862", "deps": ["npm:@nestjs/mongoose", "npm:mongoose", "npm:@nestjs/graphql"]}, {"file": "apps/identity/src/entities/service-line.entity.ts", "hash": "18203989524713339233", "deps": ["npm:@nestjs/mongoose", "npm:mongoose", "npm:@nestjs/graphql"]}, {"file": "apps/identity/src/entities/user.entity.ts", "hash": "7244914812579065643", "deps": ["npm:@nestjs/mongoose", "npm:mongoose", "npm:@nestjs/graphql"]}, {"file": "apps/identity/src/environments/environment.production.ts", "hash": "1180766962835372686"}, {"file": "apps/identity/src/environments/environment.ts", "hash": "7521450825583949554"}, {"file": "apps/identity/src/guards/jwt-auth.guard.ts", "hash": "3367962976078463514", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/graphql", "npm:@nestjs/jwt"]}, {"file": "apps/identity/src/identity.module.ts", "hash": "1198572985657046316", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:@nestjs/jwt", "npm:@nestjs/passport", "npm:@nestjs/graphql", "npm:@nestjs/apollo", "npm:path", "db", "email"]}, {"file": "apps/identity/src/main.ts", "hash": "16833977942206731958", "deps": ["npm:@nestjs/core", "npm:express"]}, {"file": "apps/identity/src/resolvers/auth.resolver.ts", "hash": "2529475907694549457", "deps": ["npm:@nestjs/graphql", "npm:@nestjs/common"]}, {"file": "apps/identity/src/resolvers/user.resolver.ts", "hash": "8526950448979688247", "deps": ["npm:@nestjs/graphql", "npm:@nestjs/mongoose", "npm:mongoose"]}, {"file": "apps/identity/src/services/auth.service.ts", "hash": "5648490557818505269", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:mongoose", "npm:@nestjs/jwt", "npm:bcryptjs", "npm:crypto", "email"]}, {"file": "apps/identity/src/services/seed.service.ts", "hash": "16375475836472321445", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:mongoose"]}, {"file": "apps/identity/src/strategies/jwt.strategy.ts", "hash": "7723482287561222240", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "npm:passport-jwt", "npm:@nestjs/mongoose", "npm:mongoose"]}, {"file": "apps/identity/src/utils/otp.util.ts", "hash": "10664233347951891356"}, {"file": "apps/identity/src/utils/token.util.ts", "hash": "2161613957447286528", "deps": ["npm:crypto"]}, {"file": "apps/identity/tsconfig.app.json", "hash": "6309631244078275332"}, {"file": "apps/identity/tsconfig.json", "hash": "13792366361973949391"}, {"file": "apps/identity/tsconfig.spec.json", "hash": "3031220043730143498"}, {"file": "apps/identity/webpack.config.js", "hash": "12934181458006851964", "deps": ["npm:@nx/webpack"]}], "db": [{"file": "libs/db/jest.config.ts", "hash": "15629053696912452733"}, {"file": "libs/db/project.json", "hash": "12412168824483924236"}, {"file": "libs/db/src/index.ts", "hash": "2627190201969948778"}, {"file": "libs/db/src/lib/database.module.ts", "hash": "11197611648849274157", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose"]}, {"file": "libs/db/src/lib/db.module.ts", "hash": "16696461979567675896", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:@nestjs/config"]}, {"file": "libs/db/src/lib/db.service.ts", "hash": "14110169253112316891", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:mongoose"]}, {"file": "libs/db/src/lib/services/mongo-connection.service.ts", "hash": "10109367986333641519", "deps": ["npm:@nestjs/common"]}, {"file": "libs/db/tsconfig.json", "hash": "13141995473148530199"}, {"file": "libs/db/tsconfig.lib.json", "hash": "12770638838408525368"}, {"file": "libs/db/tsconfig.spec.json", "hash": "9774219794243167362"}], "config": [{"file": "libs/config/jest.config.ts", "hash": "7587387215345475989"}, {"file": "libs/config/project.json", "hash": "10754605598547181433"}, {"file": "libs/config/src/index.ts", "hash": "17181726799544604482"}, {"file": "libs/config/src/lib/config.module.ts", "hash": "6670888947630346219", "deps": ["npm:@nestjs/common", "npm:@nestjs/config"]}, {"file": "libs/config/tsconfig.json", "hash": "13141995473148530199"}, {"file": "libs/config/tsconfig.lib.json", "hash": "9714485724899535731"}, {"file": "libs/config/tsconfig.spec.json", "hash": "3031220043730143498"}], "permissions": [{"file": "libs/permissions/project.json", "hash": "16395152532694554076"}, {"file": "libs/permissions/src/index.ts", "hash": "8046756639726659767"}, {"file": "libs/permissions/src/lib/permissions.module.ts", "hash": "6304265350800871047", "deps": ["npm:@nestjs/common"]}, {"file": "libs/permissions/tsconfig.lib.json", "hash": "10591961658608011074"}], "error": [{"file": "libs/error/jest.config.ts", "hash": "16863275019839618580"}, {"file": "libs/error/project.json", "hash": "16996391031551643183"}, {"file": "libs/error/src/index.ts", "hash": "10884434436419426486"}, {"file": "libs/error/src/lib/error.module.ts", "hash": "5368260438505263780", "deps": ["npm:@nestjs/common"]}, {"file": "libs/error/src/lib/exceptions/auth.exception.ts", "hash": "11670455206671350616", "deps": ["npm:@nestjs/common"]}, {"file": "libs/error/tsconfig.json", "hash": "13141995473148530199"}, {"file": "libs/error/tsconfig.lib.json", "hash": "15283078276830567470"}, {"file": "libs/error/tsconfig.spec.json", "hash": "3031220043730143498"}]}}}