{"name": "db", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/db/src", "projectType": "library", "tags": ["scope:shared", "type:data-access"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/db", "main": "libs/db/src/index.ts", "tsConfig": "libs/db/tsconfig.lib.json", "assets": ["libs/db/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/db/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/db/jest.config.ts", "passWithNoTests": true}}}}