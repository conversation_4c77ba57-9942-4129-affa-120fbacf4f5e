{"name": "email", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/email/src", "projectType": "library", "tags": ["scope:shared", "type:feature"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/email", "main": "libs/email/src/index.ts", "tsConfig": "libs/email/tsconfig.lib.json", "assets": ["libs/email/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/email/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/email/jest.config.ts", "passWithNoTests": true}}}}