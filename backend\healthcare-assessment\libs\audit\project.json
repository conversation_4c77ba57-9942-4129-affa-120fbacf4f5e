{"name": "audit", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/audit/src", "projectType": "library", "tags": ["scope:shared", "type:feature"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/audit", "main": "libs/audit/src/index.ts", "tsConfig": "libs/audit/tsconfig.lib.json", "assets": ["libs/audit/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/audit/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/audit/jest.config.ts", "passWithNoTests": true}}}}