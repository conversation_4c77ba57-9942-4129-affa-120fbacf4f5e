import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from '../entities/user.entity';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET || 'healthcare-assessment-secret',
    });
  }

  async validate(payload: any) {
    const user = await this.userModel.findById(payload.userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }
    return {
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
      facilityName: payload.facilityName,
    };
  }
}
