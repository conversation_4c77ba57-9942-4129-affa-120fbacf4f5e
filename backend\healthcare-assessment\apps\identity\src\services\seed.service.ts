import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { FacilityType } from '../entities/facility-type.entity';
import { ServiceLine } from '../entities/service-line.entity';

@Injectable()
export class SeedService implements OnModuleInit {
  constructor(
    @InjectModel(FacilityType.name) private facilityTypeModel: Model<FacilityType>,
    @InjectModel(ServiceLine.name) private serviceLineModel: Model<ServiceLine>,
  ) {}

  async onModuleInit() {
    await this.seedFacilityTypes();
    await this.seedServiceLines();
  }

  private async seedFacilityTypes() {
    const count = await this.facilityTypeModel.countDocuments();
    if (count === 0) {
      const facilityTypes = [
        { name: 'Hospital', description: 'General Hospital' },
        { name: 'Clinic', description: 'Medical Clinic' },
        { name: 'Nursing Home', description: 'Long-term Care Facility' },
        { name: 'Rehabilitation Center', description: 'Rehabilitation Facility' },
      ];

      await this.facilityTypeModel.insertMany(facilityTypes);
      console.log('Facility types seeded successfully');
    }
  }

  private async seedServiceLines() {
    const count = await this.serviceLineModel.countDocuments();
    if (count === 0) {
      const serviceLines = [
        { name: 'Emergency Medicine', description: 'Emergency care services' },
        { name: 'Cardiology', description: 'Heart and cardiovascular care' },
        { name: 'Orthopedics', description: 'Bone and joint care' },
        { name: 'Neurology', description: 'Brain and nervous system care' },
        { name: 'Pediatrics', description: 'Children healthcare' },
        { name: 'Oncology', description: 'Cancer treatment and care' },
      ];

      await this.serviceLineModel.insertMany(serviceLines);
      console.log('Service lines seeded successfully');
    }
  }
}