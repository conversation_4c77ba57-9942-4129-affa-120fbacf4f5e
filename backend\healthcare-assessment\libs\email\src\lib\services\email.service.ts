import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'localhost',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  async sendOtpEmail(email: string, otpCode: string): Promise<void> {
    await this.transporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'OTP Verification - Healthcare Assessment',
      html: `
        <h2>Email Verification</h2>
        <p>Your OTP code is: <strong>${otpCode}</strong></p>
        <p>This code will expire in 10 minutes.</p>
      `,
    });
  }

  async sendWelcomeEmail(email: string, fullName: string): Promise<void> {
    await this.transporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'Welcome to Healthcare Assessment',
      html: `
        <h2>Welcome ${fullName}!</h2>
        <p>Your registration has been completed successfully.</p>
        <p>You can now sign in to your account.</p>
      `,
    });
  }

  async sendPasswordResetEmail(email: string, resetToken: string): Promise<void> {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

    await this.transporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'Password Reset - Healthcare Assessment',
      html: `
        <h2>Password Reset Request</h2>
        <p>Click the link below to reset your password:</p>
        <a href="${resetUrl}">Reset Password</a>
        <p>This link will expire in 30 minutes.</p>
      `,
    });
  }
}
