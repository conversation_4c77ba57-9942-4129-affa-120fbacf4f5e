import { Module } from '@nestjs/common';

export enum HttpStatus {
  OK = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  NOT_FOUND = 404,
  INTERNAL_SERVER_ERROR = 500,
}

export enum ResponseCode {
  SUCCESS = 'SUCCESS',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  OTP_LIMIT_REACHED = 'OTP_LIMIT_REACHED',
  INVALID_OTP = 'INVALID_OTP',
}

export enum ErrorType {
  IDENTITY = 'IDENTITY',
  VALIDATION = 'VALIDATION',
  DATABASE = 'DATABASE',
}

export class Error {
  constructor(
    public status: HttpStatus,
    public code: ResponseCode,
    public type: ErrorType,
    public message: string,
  ) { }

  toJSON() {
    return {
      status: this.status,
      code: this.code,
      type: this.type,
      message: this.message,
      success: false,
    };
  }
}

export class Success {
  constructor(
    public status: HttpStatus,
    public code: ResponseCode,
    public type: ErrorType,
    public data: any,
  ) { }

  toJSON() {
    return {
      status: this.status,
      code: this.code,
      type: this.type,
      data: this.data,
      success: true,
    };
  }
}

@Module({})
export class ErrorModule { }
