{"name": "config", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/config/src", "projectType": "library", "tags": ["scope:shared", "type:util"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/config", "main": "libs/config/src/index.ts", "tsConfig": "libs/config/tsconfig.lib.json", "assets": ["libs/config/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/config/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/config/jest.config.ts", "passWithNoTests": true}}}}